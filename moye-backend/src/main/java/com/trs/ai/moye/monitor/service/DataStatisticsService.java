package com.trs.ai.moye.monitor.service;


import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.monitor.entity.AccessTaskUnit;
import com.trs.ai.moye.monitor.request.StatisticRequest;
import com.trs.ai.moye.monitor.response.statistics.TaskStatisticsListResponse;
import com.trs.moye.base.common.exception.BizException;

/**
 * 数据统计相关接口
 *
 * <AUTHOR>
 * @since 2024/8/27 16:45
 */
public interface DataStatisticsService {


    /**
     * 接入数据每个贴源库详情
     *
     * @param request 前端请求参数
     * @return com.trs.ai.my.entity.response.AccessDetailsResponse
     * <AUTHOR>
     * @since 2024/8/28 14:24
     */

    PageResponse<AccessTaskUnit> getAccessDetailList(StatisticRequest request) throws BizException;

    /**
     * 任务统计列表
     *
     * @param request 请求参数
     * @return 分页信息
     */
    PageResponse<TaskStatisticsListResponse> taskList(StatisticRequest request) throws BizException;

}
